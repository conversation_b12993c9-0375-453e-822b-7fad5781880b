@import "tailwindcss";

@import url('https://fonts.googleapis.com/css2?family=Cormorant+Garamond:wght@300;400;500;600;700&family=Playfair+Display:ital,wght@0,400..900;1,400..900&family=Poppins:wght@300;400;500&display=swap');

@layer base {
  body {
    background-color: #111111;
    color: #FFFFFF;
    font-family: 'Poppins', sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}

@layer components {
  .font-serif-display {
    font-family: 'Playfair Display', serif;
  }

  /* Custom letter spacing to match the design */
  .tracking-widest-plus {
    letter-spacing: 0.25em;
  }

  /* Custom styles for service numbers */
  .service-number {
    position: absolute;
    left: -1.5rem;
    bottom: -1.5rem;
    font-size: 5rem;
    line-height: 1;
    color: rgba(255, 255, 255, 0.15);
    z-index: -1;
  }

  /* Gallery styles */
  .gallery {
    position: relative;
    overflow: hidden;
  }

  .gallery-track {
    position: relative;
    width: 100%;
    will-change: transform;
  }

  .card {
    height: 400px;
    overflow: hidden;
  }

  .card-image-wrapper {
    height: 135%;
    will-change: transform;
  }

  .card img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}
